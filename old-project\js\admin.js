// Admin Panel JavaScript
let products = [];
let editingProductId = null;

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    // LocalStorage'dan ürünleri yükle
    loadProductsFromStorage();
    
    // Giriş formunu ayarla
    setupLoginForm();
    
    // Ürün formunu ayarla
    setupProductForm();
    
    // Giriş durumunu kontrol et
    checkLoginStatus();
});

// LocalStorage'dan ürünleri yükle
function loadProductsFromStorage() {
    const storedProducts = localStorage.getItem('marketProducts');
    if (storedProducts) {
        products = JSON.parse(storedProducts);
    } else {
        // Varsayılan ürünler
        products = [
            {
                id: 1,
                name: "Ta<PERSON>üt",
                description: "Günlük taze çiğ süt, 1 litre",
                price: "15.90 TL",
                category: "dairy",
                image: "images/milk.jpg"
            },
            {
                id: 2,
                name: "Ekmek",
                description: "Taze köy ekmeği, 500gr",
                price: "8.50 TL",
                category: "breakfast",
                image: "images/bread.jpg"
            },
            {
                id: 3,
                name: "Elma",
                description: "Amasya elması, 1kg",
                price: "12.90 TL",
                category: "fruits",
                image: "images/apple.jpg"
            },
            {
                id: 4,
                name: "Su",
                description: "Doğal kaynak suyu, 5 litre",
                price: "18.50 TL",
                category: "drinks",
                image: "images/water.jpg"
            },
            {
                id: 5,
                name: "Cips",
                description: "Patates cipsi, 150gr",
                price: "22.90 TL",
                category: "snacks",
                image: "images/chips.jpg"
            },
            {
                id: 6,
                name: "Kahve",
                description: "Türk kahvesi, 100gr",
                price: "35.00 TL",
                category: "drinks",
                image: "images/placeholder.jpg"
            },
            {
                id: 7,
                name: "Çikolata",
                description: "Bitter çikolata, 80gr",
                price: "19.90 TL",
                category: "snacks",
                image: "images/placeholder.jpg"
            },
            {
                id: 8,
                name: "Yoğurt",
                description: "Ev yapımı yoğurt, 1kg",
                price: "24.90 TL",
                category: "dairy",
                image: "images/placeholder.jpg"
            }
        ];
        saveProductsToStorage();
    }
}

// LocalStorage'a ürünleri kaydet
function saveProductsToStorage() {
    localStorage.setItem('marketProducts', JSON.stringify(products));
}

// Giriş durumunu kontrol et
function checkLoginStatus() {
    const isLoggedIn = sessionStorage.getItem('adminLoggedIn');
    if (isLoggedIn === 'true') {
        showDashboard();
    } else {
        showLoginForm();
    }
}

// Giriş formunu göster
function showLoginForm() {
    document.getElementById('loginForm').style.display = 'flex';
    document.getElementById('adminDashboard').style.display = 'none';
}

// Dashboard'u göster
function showDashboard() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('adminDashboard').style.display = 'block';
    loadProductsTable();
    updateStats();
}

// Giriş formunu ayarla
function setupLoginForm() {
    const loginForm = document.getElementById('adminLoginForm');
    
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        // Basit doğrulama (gerçek projede daha güvenli olmalı)
        if (email === '<EMAIL>' && password === 'admin123') {
            sessionStorage.setItem('adminLoggedIn', 'true');
            showDashboard();
            showNotification('Başarıyla giriş yapıldı!', 'success');
        } else {
            showNotification('Hatalı e-posta veya şifre!', 'error');
        }
    });
}

// Ürün formunu ayarla
function setupProductForm() {
    const addProductBtn = document.getElementById('addProductBtn');
    const productForm = document.getElementById('productForm');
    const addProductForm = document.getElementById('addProductForm');
    const cancelBtn = document.getElementById('cancelBtn');
    
    // Yeni ürün ekleme butonu
    addProductBtn.addEventListener('click', function() {
        editingProductId = null;
        document.getElementById('formTitle').textContent = 'Yeni Ürün Ekle';
        addProductForm.reset();
        productForm.style.display = 'block';
        addProductBtn.style.display = 'none';
    });
    
    // Form gönderimi
    addProductForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(addProductForm);
        const productData = {
            name: formData.get('name'),
            description: formData.get('description'),
            price: formData.get('price'),
            category: formData.get('category'),
            image: formData.get('image') || 'images/placeholder.jpg'
        };
        
        if (editingProductId) {
            // Ürün güncelle
            updateProduct(editingProductId, productData);
        } else {
            // Yeni ürün ekle
            addProduct(productData);
        }
        
        // Formu kapat
        hideProductForm();
    });
    
    // İptal butonu
    cancelBtn.addEventListener('click', function() {
        hideProductForm();
    });
}

// Ürün formunu gizle
function hideProductForm() {
    document.getElementById('productForm').style.display = 'none';
    document.getElementById('addProductBtn').style.display = 'block';
    editingProductId = null;
}

// Yeni ürün ekle
function addProduct(productData) {
    const newProduct = {
        id: Date.now(), // Basit ID oluşturma
        ...productData
    };
    
    products.push(newProduct);
    saveProductsToStorage();
    loadProductsTable();
    updateStats();
    showNotification('Ürün başarıyla eklendi!', 'success');
}

// Ürün güncelle
function updateProduct(productId, productData) {
    const index = products.findIndex(p => p.id === productId);
    if (index !== -1) {
        products[index] = { ...products[index], ...productData };
        saveProductsToStorage();
        loadProductsTable();
        showNotification('Ürün başarıyla güncellendi!', 'success');
    }
}

// Ürün sil
function deleteProduct(productId) {
    if (confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
        products = products.filter(p => p.id !== productId);
        saveProductsToStorage();
        loadProductsTable();
        updateStats();
        showNotification('Ürün başarıyla silindi!', 'success');
    }
}

// Ürün düzenleme formunu aç
function editProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        editingProductId = productId;
        document.getElementById('formTitle').textContent = 'Ürün Düzenle';
        
        // Form alanlarını doldur
        document.getElementById('productName').value = product.name;
        document.getElementById('productDescription').value = product.description;
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productImage').value = product.image;
        
        // Formu göster
        document.getElementById('productForm').style.display = 'block';
        document.getElementById('addProductBtn').style.display = 'none';
    }
}

// Ürün tablosunu yükle
function loadProductsTable() {
    const tableBody = document.getElementById('productsTableBody');
    
    tableBody.innerHTML = products.map(product => `
        <tr>
            <td>
                <img src="${product.image}" alt="${product.name}" class="product-image-small" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0zMCAzMEMzMzMuMTM4IDMwIDM1IDI2Ljg2MiAzNSAyM0MzNSAxOS4xMzgxIDMzLjEzOCAxNiAzMCAxNkMyNi44NjIgMTYgMjUgMTkuMTM4MSAyNSAyM0MyNSAyNi44NjIgMjYuODYyIDMwIDMwIDMwWiIgZmlsbD0iI0M0QzVDNyIvPgo8cGF0aCBkPSJNMzAgMzVDMzMuMTM4IDM1IDM1IDMxLjg2MiAzNSAyOEMzNSAyNC4xMzgxIDMzLjEzOCAyMSAzMCAyMUMyNi44NjIgMjEgMjUgMjQuMTM4MSAyNSAyOEMyNSAzMS44NjIgMjYuODYyIDM1IDMwIDM1WiIgZmlsbD0iI0M0QzVDNyIvPgo8cGF0aCBkPSJNMzAgNDBDMzMuMTM4IDQwIDM1IDM2Ljg2MiAzNSAzM0MzNSAyOS4xMzgxIDMzLjEzOCAyNiAzMCAyNkMyNi44NjIgMjYgMjUgMjkuMTM4MSAyNSAzM0MyNSAzNi44NjIgMjYuODYyIDQwIDMwIDQwWiIgZmlsbD0iI0M0QzVDNyIvPgo8L3N2Zz4K'">
            </td>
            <td>${product.name}</td>
            <td>${getCategoryName(product.category)}</td>
            <td>${product.price}</td>
            <td>
                <div class="action-buttons">
                    <button class="edit-btn" onclick="editProduct(${product.id})" title="Düzenle">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-btn" onclick="deleteProduct(${product.id})" title="Sil">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Kategori adını Türkçe'ye çevir
function getCategoryName(category) {
    const categoryNames = {
        'drinks': 'İçecekler',
        'snacks': 'Atıştırmalıklar',
        'breakfast': 'Kahvaltılık',
        'dairy': 'Süt Ürünleri',
        'fruits': 'Meyve & Sebze'
    };
    return categoryNames[category] || category;
}

// İstatistikleri güncelle
function updateStats() {
    document.getElementById('totalProducts').textContent = products.length;
}

// Bildirim göster
function showNotification(message, type = 'info') {
    // Basit bildirim sistemi
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    if (type === 'success') {
        notification.style.backgroundColor = '#27ae60';
    } else if (type === 'error') {
        notification.style.backgroundColor = '#e74c3c';
    } else {
        notification.style.backgroundColor = '#3498db';
    }
    
    document.body.appendChild(notification);
    
    // 3 saniye sonra kaldır
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// CSS animasyonları ekle
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Çıkış yap
function logout() {
    sessionStorage.removeItem('adminLoggedIn');
    showLoginForm();
    showNotification('Başarıyla çıkış yapıldı!', 'success');
}

// Çıkış butonunu ayarla
document.addEventListener('DOMContentLoaded', function() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}); 