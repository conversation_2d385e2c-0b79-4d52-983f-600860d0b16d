/* Admin Panel CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #111;
    color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Admin Header */
.admin-header {
    background: #222;
    color: #fff;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-logo h2 {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff;
}

.admin-logo i {
    margin-right: 0.5rem;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #fff;
}

.logout-btn {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 5px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Login Form */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #111;
}

.login-box {
    background: #222;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    width: 100%;
    max-width: 400px;
}

.login-box h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #fff;
}

.login-box i {
    color: #27ae60;
    margin-right: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.login-btn {
    width: 100%;
    padding: 1rem;
    background: #27ae60;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s;
}

.login-btn:hover {
    background: #219150;
}

.login-info {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.login-info p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

/* Admin Dashboard */
.admin-dashboard {
    padding: 2rem 0;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #222;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 2.5rem;
    color: #27ae60;
}

.stat-info h3 {
    font-size: 2rem;
    color: #fff;
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: #ccc;
    font-weight: 500;
}

/* Admin Content */
.admin-content {
    background: #222;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.content-header {
    padding: 2rem;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-header h2 {
    color: #fff;
    font-size: 1.5rem;
}

.content-header i {
    color: #27ae60;
    margin-right: 0.5rem;
}

.add-btn {
    background: #27ae60;
    color: #fff;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s;
}

.add-btn:hover {
    background: #219150;
}

/* Product Form */
.product-form {
    padding: 2rem;
    border-bottom: 1px solid #444;
    background-color: #222;
}

.product-form h3 {
    margin-bottom: 1.5rem;
    color: #fff;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #fff;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #444;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #333;
    color: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #27ae60;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.save-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.save-btn:hover {
    background: #229954;
}

.cancel-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.cancel-btn:hover {
    background: #c0392b;
}

/* Products Table */
.products-table {
    overflow-x: auto;
}

.products-table table {
    width: 100%;
    border-collapse: collapse;
    background: #222;
    color: #fff;
}

.products-table th,
.products-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #444;
}

.products-table th {
    background-color: #333;
    font-weight: bold;
    color: #fff;
}

.products-table tr:hover {
    background-color: #333;
}

.product-image-small {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.edit-btn,
.delete-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.edit-btn {
    background-color: #3498db;
    color: white;
}

.edit-btn:hover {
    background-color: #2980b9;
}

.delete-btn {
    background-color: #e74c3c;
    color: white;
}

.delete-btn:hover {
    background-color: #c0392b;
}

/* Back to Home */
.back-to-home {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
}

.back-to-home a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 1rem 1.5rem;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.back-to-home a:hover {
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-nav {
        flex-direction: column;
        gap: 1rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .content-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .products-table {
        font-size: 0.9rem;
    }
    
    .products-table th,
    .products-table td {
        padding: 0.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .login-box {
        padding: 2rem;
        margin: 1rem;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .content-header {
        padding: 1.5rem;
    }
    
    .product-form {
        padding: 1.5rem;
    }
} 